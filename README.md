# XAMPP Docker

Un environnement de développement XAMPP complet utilisant Docker avec Apache, PHP 8.2, MySQL 8.0 et phpMyAdmin.

## 🚀 Démarrage rapide

### Prérequis
- Docker
- Docker Compose

### Installation et démarrage

1. **Démarrer XAMPP:**
   ```bash
   ./start-xampp.sh
   ```

2. **Accéder aux services:**
   - **Site web:** http://localhost
   - **phpMyAdmin:** http://localhost:8080

### 🛑 Arrêt

```bash
./stop-xampp.sh
```

## 📁 Structure du projet

```
uok/
├── htdocs/                 # Vos fichiers PHP/HTML (DocumentRoot)
│   ├── index.php          # Page d'accueil avec tests
│   ├── phpinfo.php        # Informations PHP
│   └── test-db.php        # Tests de base de données
├── config/                # Configurations
│   ├── php/php.ini        # Configuration PHP
│   ├── apache/apache2.conf # Configuration Apache
│   └── mysql/my.cnf       # Configuration MySQL
├── docker-compose.yml     # Configuration des services
├── Dockerfile            # Image PHP personnalisée
├── start-xampp.sh        # Script de démarrage
└── stop-xampp.sh         # Script d'arrêt
```

## 🗄️ Base de données

### Informations de connexion

- **Host:** `mysql` (depuis PHP) ou `localhost` (depuis votre machine)
- **Port:** `3306`
- **Base de données:** `test_db`
- **Utilisateur:** `xampp`
- **Mot de passe:** `xampp`
- **Root password:** `root`

### Exemple de connexion PHP

```php
<?php
try {
    $pdo = new PDO('mysql:host=mysql;dbname=test_db', 'xampp', 'xampp');
    echo "Connexion réussie !";
} catch (PDOException $e) {
    echo "Erreur : " . $e->getMessage();
}
?>
```

## 🔧 Services inclus

### Apache + PHP 8.2
- **Port:** 80 (HTTP), 443 (HTTPS)
- **Extensions:** mysqli, pdo_mysql, gd, zip, mbstring, curl, xml, intl, opcache
- **Modules Apache:** rewrite, headers

### MySQL 8.0
- **Port:** 3306
- **Charset:** utf8mb4
- **Storage Engine:** InnoDB

### phpMyAdmin
- **Port:** 8080
- **Accès:** http://localhost:8080
- **Login:** root / root

## 📝 Développement

1. **Placez vos fichiers PHP dans le dossier `htdocs/`**
2. **Accédez à vos fichiers via:** http://localhost/votre-fichier.php
3. **Les modifications sont automatiquement reflétées** (volumes montés)

## 🛠️ Commandes utiles

### Docker Compose
```bash
# Démarrer en arrière-plan
docker-compose up -d

# Voir les logs
docker-compose logs

# Redémarrer un service
docker-compose restart web

# Arrêter et supprimer (avec données)
docker-compose down -v

# Reconstruire les images
docker-compose build --no-cache
```

### Accès aux conteneurs
```bash
# Shell dans le conteneur web
docker exec -it xampp_web bash

# Shell dans le conteneur MySQL
docker exec -it xampp_mysql mysql -u root -p
```

## 🔍 Dépannage

### Ports déjà utilisés
Si les ports 80, 3306 ou 8080 sont déjà utilisés, modifiez les ports dans `docker-compose.yml`:

```yaml
ports:
  - "8000:80"  # Changez 80 en 8000 par exemple
```

### Problèmes de permissions
```bash
# Réparer les permissions
sudo chown -R $USER:$USER htdocs/
```

### Logs des services
```bash
# Logs Apache
docker-compose logs web

# Logs MySQL
docker-compose logs mysql

# Logs phpMyAdmin
docker-compose logs phpmyadmin
```

## 📊 Monitoring

- **Statut des conteneurs:** `docker-compose ps`
- **Utilisation des ressources:** `docker stats`
- **Logs en temps réel:** `docker-compose logs -f`

## 🎯 Fonctionnalités

✅ **Apache 2.4** avec mod_rewrite activé  
✅ **PHP 8.2** avec extensions courantes  
✅ **MySQL 8.0** avec configuration optimisée  
✅ **phpMyAdmin** pour la gestion de base de données  
✅ **Volumes persistants** pour les données MySQL  
✅ **Configuration personnalisable**  
✅ **Scripts de démarrage/arrêt simples**  
✅ **Pages de test incluses**  

Bon développement ! 🚀
