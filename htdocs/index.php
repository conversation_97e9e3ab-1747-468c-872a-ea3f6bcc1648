<?php
// Page d'accueil XAMPP
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAMPP Docker - Bienvenue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #007acc;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .link-card {
            background: #007acc;
            color: white;
            padding: 20px;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: background 0.3s;
        }
        .link-card:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 XAMPP Docker</h1>
            <p>Votre environnement de développement est prêt !</p>
        </div>

        <div class="info-box success">
            <h3>✅ Statut du serveur</h3>
            <p><strong>Apache:</strong> Actif</p>
            <p><strong>PHP:</strong> Version <?php echo phpversion(); ?></p>
            <p><strong>MySQL:</strong> Disponible sur le port 3306</p>
        </div>

        <div class="info-box">
            <h3>📊 Informations PHP</h3>
            <p><strong>Date/Heure:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>Serveur:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
            <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
        </div>

        <div class="info-box">
            <h3>🔗 Test de connexion MySQL</h3>
            <?php
            try {
                $pdo = new PDO('mysql:host=mysql;dbname=test_db', 'xampp', 'xampp');
                echo '<p style="color: green;">✅ Connexion MySQL réussie !</p>';
                
                // Test simple
                $stmt = $pdo->query('SELECT VERSION() as version');
                $version = $stmt->fetch();
                echo '<p><strong>Version MySQL:</strong> ' . $version['version'] . '</p>';
            } catch (PDOException $e) {
                echo '<p style="color: red;">❌ Erreur de connexion MySQL: ' . $e->getMessage() . '</p>';
            }
            ?>
        </div>

        <div class="links">
            <a href="phpinfo.php" class="link-card">
                <h4>📋 PHP Info</h4>
                <p>Informations détaillées PHP</p>
            </a>
            <a href="http://localhost:8080" class="link-card" target="_blank">
                <h4>🗄️ phpMyAdmin</h4>
                <p>Gestion de base de données</p>
            </a>
            <a href="test-db.php" class="link-card">
                <h4>🧪 Test Database</h4>
                <p>Test des opérations DB</p>
            </a>
        </div>
    </div>
</body>
</html>
