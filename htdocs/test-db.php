<?php
// Test des opérations de base de données
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Base de Données - XAMPP Docker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Base de Données</h1>
        <p><a href="index.php">← Retour à l'accueil</a></p>

        <?php
        try {
            // Connexion à la base de données
            $pdo = new PDO('mysql:host=mysql;dbname=test_db', 'xampp', 'xampp');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo '<div class="result success">✅ Connexion à MySQL réussie !</div>';

            // Création d'une table de test
            $createTable = "
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    nom VARCHAR(100) NOT NULL,
                    email VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ";
            $pdo->exec($createTable);
            echo '<div class="result success">✅ Table "users" créée/vérifiée</div>';

            // Insertion de données de test
            $insertData = "
                INSERT IGNORE INTO users (id, nom, email) VALUES 
                (1, 'Jean Dupont', '<EMAIL>'),
                (2, 'Marie Martin', '<EMAIL>'),
                (3, 'Pierre Durand', '<EMAIL>')
            ";
            $pdo->exec($insertData);
            echo '<div class="result success">✅ Données de test insérées</div>';

            // Lecture des données
            $stmt = $pdo->query('SELECT * FROM users ORDER BY id');
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo '<h3>📊 Données dans la table "users":</h3>';
            if ($users) {
                echo '<table>';
                echo '<tr><th>ID</th><th>Nom</th><th>Email</th><th>Créé le</th></tr>';
                foreach ($users as $user) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($user['id']) . '</td>';
                    echo '<td>' . htmlspecialchars($user['nom']) . '</td>';
                    echo '<td>' . htmlspecialchars($user['email']) . '</td>';
                    echo '<td>' . htmlspecialchars($user['created_at']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<div class="result">Aucune donnée trouvée</div>';
            }

            // Test d'une requête avec paramètres
            $stmt = $pdo->prepare('SELECT COUNT(*) as total FROM users');
            $stmt->execute();
            $count = $stmt->fetch();
            echo '<div class="result success">📈 Nombre total d\'utilisateurs: ' . $count['total'] . '</div>';

        } catch (PDOException $e) {
            echo '<div class="result error">❌ Erreur: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>

        <h3>💡 Informations de connexion</h3>
        <div class="result">
            <strong>Host:</strong> mysql<br>
            <strong>Database:</strong> test_db<br>
            <strong>Username:</strong> xampp<br>
            <strong>Password:</strong> xampp<br>
            <strong>Root Password:</strong> root
        </div>
    </div>
</body>
</html>
