#!/bin/bash

# Script de démarrage XAMPP Docker
echo "🚀 Démarrage de XAMPP Docker..."

# Vérification que Docker est installé
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Construction et démarrage des conteneurs
echo "📦 Construction des images Docker..."
docker-compose build

echo "🔄 Démarrage des services..."
docker-compose up -d

# Attendre que les services soient prêts
echo "⏳ Attente du démarrage des services..."
sleep 10

# Vérification du statut
echo "📊 Statut des services:"
docker-compose ps

echo ""
echo "✅ XAMPP Docker est maintenant en cours d'exécution !"
echo ""
echo "🌐 Accès aux services:"
echo "   • Site web: http://localhost"
echo "   • phpMyAdmin: http://localhost:8080"
echo ""
echo "🗄️ Informations de base de données:"
echo "   • Host: localhost (ou mysql depuis PHP)"
echo "   • Port: 3306"
echo "   • Database: test_db"
echo "   • Username: xampp"
echo "   • Password: xampp"
echo "   • Root password: root"
echo ""
echo "📁 Vos fichiers PHP doivent être placés dans le dossier 'htdocs/'"
echo ""
echo "🛑 Pour arrêter XAMPP: ./stop-xampp.sh ou docker-compose down"
