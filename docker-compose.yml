version: '3.8'

services:
  # Apache + PHP Service
  web:
    build: .
    container_name: xampp_web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./htdocs:/var/www/html
      - ./config/php/php.ini:/usr/local/etc/php/php.ini
      - ./config/apache/apache2.conf:/etc/apache2/apache2.conf
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    depends_on:
      - mysql
    networks:
      - xampp-network
    restart: unless-stopped

  # MySQL Service
  mysql:
    image: mysql:8.0
    container_name: xampp_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: test_db
      MYSQL_USER: xampp
      MYSQL_PASSWORD: xampp
    volumes:
      - mysql_data:/var/lib/mysql
      - ./config/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - xampp-network
    restart: unless-stopped

  # phpMyAdmin Service
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: xampp_phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root
      MYSQL_ROOT_PASSWORD: root
    depends_on:
      - mysql
    networks:
      - xampp-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  xampp-network:
    driver: bridge
